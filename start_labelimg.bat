@echo off
echo 🏷️ 启动LabelImg标注工具...
echo.
echo 📁 图片目录: %cd%\data\raw
echo 📝 标注输出: %cd%\data\annotations
echo.
echo ================================================== 
echo 📋 LabelImg使用说明:
echo 1. 点击 'Open Dir' 选择图片目录
echo 2. 点击 'Change Save Dir' 选择标注保存目录
echo 3. 点击左下角 'PascalVOC' 切换到 'YOLO' 格式
echo 4. 按 'W' 键开始画框标注
echo 5. 选择正确的工件类别 (工件A/工件B/工件C)
echo 6. 按 'Ctrl+S' 保存当前标注
echo 7. 按 'D' 键切换到下一张图片
echo ==================================================
echo.

conda activate pytorch
python -c "from labelImg import labelImg; import sys; app = labelImg.QApplication(sys.argv); win = labelImg.MainWindow(); win.show(); sys.exit(app.exec_())"

pause

#!/usr/bin/env python3
"""
启动自定义工件标注工具
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """启动自定义标注工具"""
    
    print("🏷️ 启动自定义工件标注工具...")
    print("="*50)
    print("📋 功能特点:")
    print("✅ 中文界面")
    print("✅ 详细工件信息标注 (名称、编号、颜色、形状、材料、描述)")
    print("✅ 可视化框选工件")
    print("✅ 标注信息管理")
    print("✅ 支持保存和加载")
    print("="*50)
    print("\n🖱️ 使用说明:")
    print("1. 在图片上拖拽鼠标画框选择工件")
    print("2. 在右侧填写详细的工件信息")
    print("3. 点击'添加当前工件'保存标注")
    print("4. 使用'上一张'/'下一张'切换图片")
    print("5. 点击'保存标注'保存到文件")
    print("="*50)
    
    try:
        from src.annotation_manager.custom_annotator import WorkpieceAnnotator
        
        # 启动应用
        app = WorkpieceAnnotator()
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保所有依赖已安装: pip install pillow opencv-python")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()

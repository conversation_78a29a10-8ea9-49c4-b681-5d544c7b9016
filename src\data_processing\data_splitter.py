#!/usr/bin/env python3
"""
数据集划分模块
将标注好的数据按照8:1:1的比例划分为训练集、验证集和测试集
"""

import os
import shutil
import random
from pathlib import Path
from typing import List, Tuple

class DataSplitter:
    """数据集划分器"""
    
    def __init__(self, data_root: str, train_ratio: float = 0.8, val_ratio: float = 0.1):
        """
        初始化数据划分器
        
        Args:
            data_root: 数据根目录
            train_ratio: 训练集比例
            val_ratio: 验证集比例
        """
        self.data_root = Path(data_root)
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = 1.0 - train_ratio - val_ratio
        
        # 目录路径
        self.raw_dir = self.data_root / "raw"
        self.annotations_dir = self.data_root / "annotations"
        self.train_dir = self.data_root / "train"
        self.val_dir = self.data_root / "val"
        self.test_dir = self.data_root / "test"
        
    def get_image_annotation_pairs(self) -> List[Tuple[Path, Path]]:
        """获取图片和对应标注文件的配对列表"""
        pairs = []
        
        # 获取所有图片文件
        image_files = list(self.raw_dir.glob("*.jpg")) + list(self.raw_dir.glob("*.png"))
        
        for img_file in image_files:
            # 对应的标注文件
            annotation_file = self.annotations_dir / f"{img_file.stem}.txt"
            
            if annotation_file.exists():
                pairs.append((img_file, annotation_file))
            else:
                print(f"⚠️ 警告: 图片 {img_file.name} 没有对应的标注文件")
        
        return pairs
    
    def create_directories(self):
        """创建训练、验证、测试目录"""
        for split_dir in [self.train_dir, self.val_dir, self.test_dir]:
            (split_dir / "images").mkdir(parents=True, exist_ok=True)
            (split_dir / "labels").mkdir(parents=True, exist_ok=True)
    
    def split_data(self, seed: int = 42):
        """
        划分数据集
        
        Args:
            seed: 随机种子，确保结果可复现
        """
        print("🔄 开始数据集划分...")
        
        # 设置随机种子
        random.seed(seed)
        
        # 获取图片-标注配对
        pairs = self.get_image_annotation_pairs()
        
        if not pairs:
            print("❌ 错误: 没有找到有效的图片-标注配对")
            return
        
        print(f"📊 找到 {len(pairs)} 个有效的图片-标注配对")
        
        # 随机打乱
        random.shuffle(pairs)
        
        # 计算划分点
        total = len(pairs)
        train_end = int(total * self.train_ratio)
        val_end = train_end + int(total * self.val_ratio)
        
        # 划分数据
        train_pairs = pairs[:train_end]
        val_pairs = pairs[train_end:val_end]
        test_pairs = pairs[val_end:]
        
        print(f"📈 训练集: {len(train_pairs)} 个样本 ({len(train_pairs)/total*100:.1f}%)")
        print(f"📊 验证集: {len(val_pairs)} 个样本 ({len(val_pairs)/total*100:.1f}%)")
        print(f"📋 测试集: {len(test_pairs)} 个样本 ({len(test_pairs)/total*100:.1f}%)")
        
        # 创建目录
        self.create_directories()
        
        # 复制文件
        self._copy_files(train_pairs, self.train_dir, "训练集")
        self._copy_files(val_pairs, self.val_dir, "验证集")
        self._copy_files(test_pairs, self.test_dir, "测试集")
        
        print("✅ 数据集划分完成!")
    
    def _copy_files(self, pairs: List[Tuple[Path, Path]], target_dir: Path, split_name: str):
        """复制文件到目标目录"""
        print(f"📁 复制{split_name}文件...")
        
        for img_file, annotation_file in pairs:
            # 复制图片
            target_img = target_dir / "images" / img_file.name
            shutil.copy2(img_file, target_img)
            
            # 复制标注
            target_annotation = target_dir / "labels" / annotation_file.name
            shutil.copy2(annotation_file, target_annotation)

def main():
    """主函数"""
    # 项目根目录
    project_root = Path(__file__).parent.parent.parent
    data_root = project_root / "data"
    
    # 创建数据划分器
    splitter = DataSplitter(str(data_root))
    
    # 执行划分
    splitter.split_data()

if __name__ == "__main__":
    main()

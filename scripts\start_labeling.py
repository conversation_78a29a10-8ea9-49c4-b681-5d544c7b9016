#!/usr/bin/env python3
"""
启动LabelImg标注工具的脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def start_labelimg():
    """启动LabelImg标注工具"""
    
    # 项目根目录
    project_root = Path(__file__).parent.parent
    
    # 图片目录
    image_dir = project_root / "data" / "raw"
    
    # 标注输出目录
    annotation_dir = project_root / "data" / "annotations"
    annotation_dir.mkdir(exist_ok=True)
    
    # 类别文件
    classes_file = project_root / "data" / "classes.txt"
    
    print("🏷️ 启动LabelImg标注工具...")
    print(f"📁 图片目录: {image_dir}")
    print(f"📝 标注输出: {annotation_dir}")
    print(f"🏷️ 类别文件: {classes_file}")
    print("\n" + "="*50)
    print("📋 标注说明:")
    print("1. 选择 'Open Dir' 打开图片目录")
    print("2. 选择 'Change Save Dir' 设置标注保存目录")
    print("3. 点击 'PascalVOC' 切换到 'YOLO' 格式")
    print("4. 使用快捷键 'W' 开始标注框选")
    print("5. 为每个工件选择正确的类别标签")
    print("6. 使用 'Ctrl+S' 保存标注")
    print("7. 使用 'D' 切换到下一张图片")
    print("="*50)
    
    # 检查图片目录是否存在
    if not image_dir.exists():
        print(f"❌ 错误: 图片目录不存在 {image_dir}")
        return
    
    # 检查是否有图片
    image_files = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
    if not image_files:
        print(f"❌ 错误: 图片目录中没有找到图片文件 {image_dir}")
        return
    
    print(f"✅ 找到 {len(image_files)} 张图片待标注")
    
    try:
        # 启动LabelImg
        subprocess.run(["labelImg"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 启动LabelImg失败，请确保已正确安装")
    except FileNotFoundError:
        print("❌ 找不到labelImg命令，请确保已正确安装")
        print("💡 安装命令: pip install labelImg")

if __name__ == "__main__":
    start_labelimg()

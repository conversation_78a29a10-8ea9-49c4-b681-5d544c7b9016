# YOLO11工件识别系统

基于YOLO11的智能工件识别系统，支持详细的工件信息标注和自定义标注信息管理。

## 🚀 快速开始

### 第1步: 标注工件
```bash
python scripts/start_custom_annotator.py
```
- 点击"选择图片文件夹"导入图片
- 拖拽鼠标框选工件
- 填写详细信息（名称、编号、颜色、形状、材料、描述）
- 保存标注

### 第2步: 转换为YOLO格式
```bash
python scripts/convert_annotations.py
```

### 第3步: 数据集划分
```bash
python src/data_processing/data_splitter.py
```

### 第4步: 训练模型
```bash
python scripts/train_model.py
```

## 📁 项目结构

```
YOLO11/
├── data/                    # 数据集
│   ├── raw/                 # 原始图片
│   ├── annotations/         # JSON标注文件
│   └── train/val/test/      # 训练数据集
├── src/                     # 源代码
├── scripts/                 # 脚本文件
├── configs/                 # 配置文件
└── models/                  # 模型文件
```

## 🎯 功能特点

- ✅ **中文界面**: 完全中文化的标注工具
- ✅ **详细标注**: 支持名称、编号、颜色、形状、材料、描述等信息
- ✅ **可视化框选**: 实时预览框选区域
- ✅ **自动转换**: JSON标注自动转换为YOLO训练格式
- ✅ **智能分类**: 根据颜色+形状自动创建工件类别
- ✅ **GPU训练**: 支持CUDA加速训练

## 📋 环境要求

- Python 3.8+
- PyTorch 2.0+ (GPU版本)
- CUDA 11.8+
- 显存: 8GB+ (推荐)

## 🔧 依赖安装

```bash
pip install -r requirements.txt
```

## 📖 使用说明

1. **标注工件**: 使用自定义标注工具进行详细标注
2. **格式转换**: 自动转换为YOLO训练格式
3. **数据划分**: 按8:1:1划分训练/验证/测试集
4. **模型训练**: 使用YOLO11进行训练
5. **推理检测**: 检测新图片并显示自定义标注信息

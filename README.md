# YOLO11工件识别系统

基于YOLO11的智能工件识别系统，支持自定义标注信息管理。

## 项目特性

- 🎯 **精准识别**: 基于YOLO11的高精度工件检测
- 🏷️ **自定义标注**: 支持为每种工件设置个性化标注信息  
- 🚀 **GPU加速**: 支持CUDA加速训练和推理
- 📊 **可视化**: 直观的检测结果展示
- 🔄 **批量处理**: 支持批量图片识别

## 环境要求

- Python 3.8+
- CUDA 11.8+ (推荐)
- GPU: 8GB+ 显存 (推荐)

## 快速开始

### 1. 环境安装
```bash
pip install -r requirements.txt
```

### 2. 数据准备
1. 将原始图片放入 `data/raw/` 目录
2. 使用LabelImg进行数据标注
3. 运行数据预处理脚本

### 3. 模型训练
```bash
python scripts/train_model.py
```

### 4. 推理检测
```bash
python scripts/run_inference.py --image path/to/image.jpg
```

## 项目结构

```
YOLO11/
├── data/           # 数据集
├── models/         # 模型文件
├── src/            # 源代码
├── configs/        # 配置文件
├── scripts/        # 脚本文件
└── results/        # 结果输出
```

## 工件类别

当前支持的工件类型：
- 工件A: 红色圆形
- 工件B: 蓝色方形  
- 工件C: 绿色三角形

## 自定义标注

在 `configs/annotation_config.json` 中配置每种工件的自定义标注信息：

```json
{
  "工件A": {
    "display_name": "红色圆形工件",
    "description": "您的自定义描述信息"
  }
}
```

## 使用说明

详细使用说明请参考 `docs/` 目录下的文档。

## 许可证

MIT License

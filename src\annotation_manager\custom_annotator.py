#!/usr/bin/env python3
"""
自定义工件标注工具
支持详细的工件信息标注：颜色、材料、名称、编号等
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import json
import os
from pathlib import Path
from PIL import Image, ImageTk
import numpy as np

class WorkpieceAnnotator:
    """工件标注器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("工件标注工具")
        self.root.geometry("1200x800")
        
        # 数据
        self.current_image = None
        self.current_image_path = None
        self.image_list = []
        self.current_index = 0
        self.annotations = {}
        self.drawing = False
        self.start_x = 0
        self.start_y = 0
        self.rectangles = []
        self.temp_rectangle = None
        self.canvas_image = None
        self.scale_factor = 1.0
        
        # 项目路径
        self.project_root = Path(__file__).parent.parent.parent
        self.image_dir = self.project_root / "data" / "raw"
        self.annotation_dir = self.project_root / "data" / "annotations"
        self.annotation_dir.mkdir(exist_ok=True)
        
        self.setup_ui()
        self.load_images()
        
    def setup_ui(self):
        """设置用户界面"""
        
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧图片显示区域
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 使用Canvas替代Label以支持绘图
        self.canvas = tk.Canvas(left_frame, bg="gray")
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.start_draw)
        self.canvas.bind("<B1-Motion>", self.draw_rectangle)
        self.canvas.bind("<ButtonRelease-1>", self.end_draw)
        
        # 底部控制按钮
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(control_frame, text="选择图片文件夹", command=self.select_image_folder).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="上一张", command=self.prev_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="下一张", command=self.next_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存标注", command=self.save_annotation).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除所有框", command=self.clear_rectangles).pack(side=tk.LEFT, padx=5)
        
        # 右侧标注信息区域
        right_frame = ttk.Frame(main_frame, width=300)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_frame.pack_propagate(False)
        
        # 使用指导
        guide_frame = ttk.LabelFrame(right_frame, text="📋 使用指导")
        guide_frame.pack(fill=tk.X, pady=5)

        guide_text = """1. 点击"选择图片文件夹"导入图片
2. 在图片上拖拽鼠标画框选择工件
3. 填写右侧的工件详细信息
4. 点击"添加工件样本"保存
5. 使用"上一张"/"下一张"切换图片"""

        guide_label = ttk.Label(guide_frame, text=guide_text, justify=tk.LEFT, font=("Arial", 9))
        guide_label.pack(pady=5, padx=5)

        # 当前图片信息
        info_frame = ttk.LabelFrame(right_frame, text="📷 当前图片信息")
        info_frame.pack(fill=tk.X, pady=5)

        self.image_info_label = ttk.Label(info_frame, text="请先选择图片文件夹")
        self.image_info_label.pack(pady=5)
        
        # 工件标注信息
        annotation_frame = ttk.LabelFrame(right_frame, text="🏷️ 工件标注信息")
        annotation_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 工件基本信息
        ttk.Label(annotation_frame, text="工件名称:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.name_var = tk.StringVar()
        ttk.Entry(annotation_frame, textvariable=self.name_var, width=20).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(annotation_frame, text="工件编号:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.number_var = tk.StringVar()
        ttk.Entry(annotation_frame, textvariable=self.number_var, width=20).grid(row=1, column=1, padx=5, pady=2)
        
        ttk.Label(annotation_frame, text="颜色:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.color_var = tk.StringVar()
        color_combo = ttk.Combobox(annotation_frame, textvariable=self.color_var, width=18)
        color_combo['values'] = ('红色', '蓝色', '绿色', '黄色', '黑色', '白色', '灰色', '其他')
        color_combo.grid(row=2, column=1, padx=5, pady=2)
        
        ttk.Label(annotation_frame, text="形状:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.shape_var = tk.StringVar()
        shape_combo = ttk.Combobox(annotation_frame, textvariable=self.shape_var, width=18)
        shape_combo['values'] = ('圆形', '方形', '三角形', '长方形', '椭圆形', '不规则', '其他')
        shape_combo.grid(row=3, column=1, padx=5, pady=2)
        
        ttk.Label(annotation_frame, text="材料:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        self.material_var = tk.StringVar()
        material_combo = ttk.Combobox(annotation_frame, textvariable=self.material_var, width=18)
        material_combo['values'] = ('金属', '塑料', '木材', '陶瓷', '玻璃', '橡胶', '其他')
        material_combo.grid(row=4, column=1, padx=5, pady=2)
        
        ttk.Label(annotation_frame, text="描述信息:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=2)
        self.description_text = tk.Text(annotation_frame, width=25, height=4)
        self.description_text.grid(row=6, column=0, columnspan=2, padx=5, pady=2)
        
        # 操作按钮框架
        button_frame = ttk.Frame(annotation_frame)
        button_frame.grid(row=7, column=0, columnspan=2, pady=10)

        # 添加工件按钮
        self.add_button = ttk.Button(button_frame, text="1. 先画框选择工件", command=self.show_draw_instruction, state="normal")
        self.add_button.pack(pady=2)

        # 添加样本按钮（初始禁用）
        self.add_sample_button = ttk.Button(button_frame, text="2. 添加工件样本", command=self.add_workpiece, state="disabled")
        self.add_sample_button.pack(pady=2)

        # 状态提示标签
        self.status_label = ttk.Label(button_frame, text="请先画框选择工件区域", foreground="blue")
        self.status_label.pack(pady=2)
        
        # 已标注工件列表
        list_frame = ttk.LabelFrame(right_frame, text="📝 已标注工件列表")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.workpiece_listbox = tk.Listbox(list_frame)
        self.workpiece_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 删除选中工件按钮
        ttk.Button(list_frame, text="删除选中工件", command=self.delete_workpiece).pack(pady=5)
        
    def select_image_folder(self):
        """选择图片文件夹"""
        folder = filedialog.askdirectory(title="选择图片文件夹")
        if folder:
            self.image_dir = Path(folder)
            self.load_images()

    def load_images(self):
        """加载图片列表"""
        if self.image_dir.exists():
            self.image_list = list(self.image_dir.glob("*.jpg")) + list(self.image_dir.glob("*.png")) + list(self.image_dir.glob("*.jpeg"))
            if self.image_list:
                self.current_index = 0
                self.load_current_image()
                messagebox.showinfo("成功", f"已加载 {len(self.image_list)} 张图片")
            else:
                messagebox.showwarning("警告", "选择的文件夹中没有找到图片文件")
        else:
            messagebox.showerror("错误", f"图片目录不存在: {self.image_dir}")
    
    def load_current_image(self):
        """加载当前图片"""
        if not self.image_list:
            return
            
        self.current_image_path = self.image_list[self.current_index]
        
        # 加载图片
        image = cv2.imread(str(self.current_image_path))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 调整图片大小以适应显示
        height, width = image.shape[:2]
        max_width, max_height = 800, 600
        
        if width > max_width or height > max_height:
            scale = min(max_width/width, max_height/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height))
        
        self.current_image = image
        self.display_image()
        
        # 更新图片信息
        info_text = f"图片: {self.current_image_path.name}\n"
        info_text += f"进度: {self.current_index + 1}/{len(self.image_list)}"
        self.image_info_label.config(text=info_text)
        
        # 加载已有标注
        self.load_existing_annotation()
    
    def display_image(self):
        """显示图片"""
        if self.current_image is not None:
            # 清除canvas
            self.canvas.delete("all")

            # 转换为PIL图片
            pil_image = Image.fromarray(self.current_image)
            photo = ImageTk.PhotoImage(pil_image)

            # 在canvas上显示图片
            self.canvas_image = photo  # 保持引用
            self.canvas.create_image(0, 0, anchor=tk.NW, image=photo)

            # 更新canvas大小
            self.canvas.config(scrollregion=self.canvas.bbox("all"))

            # 绘制已有的标注框
            self.draw_existing_rectangles()
    
    def start_draw(self, event):
        """开始画框"""
        self.drawing = True
        self.start_x = event.x
        self.start_y = event.y

    def draw_rectangle(self, event):
        """画框过程 - 实时显示框选区域"""
        if self.drawing:
            # 删除之前的临时框
            if self.temp_rectangle:
                self.canvas.delete(self.temp_rectangle)

            # 绘制新的临时框
            self.temp_rectangle = self.canvas.create_rectangle(
                self.start_x, self.start_y, event.x, event.y,
                outline="red", width=2, dash=(5, 5)
            )

    def end_draw(self, event):
        """结束画框"""
        if self.drawing:
            self.drawing = False
            end_x = event.x
            end_y = event.y

            # 删除临时框
            if self.temp_rectangle:
                self.canvas.delete(self.temp_rectangle)
                self.temp_rectangle = None

            # 确保坐标有效
            if abs(end_x - self.start_x) > 10 and abs(end_y - self.start_y) > 10:
                # 添加矩形框
                rect = {
                    'x1': min(self.start_x, end_x),
                    'y1': min(self.start_y, end_y),
                    'x2': max(self.start_x, end_x),
                    'y2': max(self.start_y, end_y)
                }
                self.rectangles.append(rect)

                # 绘制永久框
                self.canvas.create_rectangle(
                    rect['x1'], rect['y1'], rect['x2'], rect['y2'],
                    outline="green", width=2
                )

                # 更新按钮状态
                self.add_button.config(text="✅ 已选择工件区域", state="disabled")
                self.add_sample_button.config(state="normal")
                self.status_label.config(text="请填写工件信息，然后点击'添加工件样本'", foreground="green")

                messagebox.showinfo("提示", "框选完成！请在右侧填写工件信息，然后点击'添加工件样本'")
    
    def show_draw_instruction(self):
        """显示画框指导"""
        messagebox.showinfo("画框指导",
                           "请在图片上用鼠标拖拽画框：\n\n"
                           "1. 按住鼠标左键\n"
                           "2. 拖拽到工件边界\n"
                           "3. 松开鼠标完成框选\n\n"
                           "画框时会显示红色虚线预览")

    def add_workpiece(self):
        """添加工件样本"""
        if not self.rectangles:
            messagebox.showwarning("警告", "请先画框选择工件区域")
            return

        if not self.name_var.get().strip():
            messagebox.showwarning("警告", "请输入工件名称")
            return

        if not self.color_var.get():
            messagebox.showwarning("警告", "请选择工件颜色")
            return

        if not self.shape_var.get():
            messagebox.showwarning("警告", "请选择工件形状")
            return

        # 使用最后一个框
        rect = self.rectangles[-1]

        workpiece = {
            'name': self.name_var.get().strip(),
            'number': self.number_var.get().strip(),
            'color': self.color_var.get(),
            'shape': self.shape_var.get(),
            'material': self.material_var.get(),
            'description': self.description_text.get(1.0, tk.END).strip(),
            'bbox': rect
        }

        # 添加到当前图片的标注中
        if str(self.current_image_path) not in self.annotations:
            self.annotations[str(self.current_image_path)] = []

        self.annotations[str(self.current_image_path)].append(workpiece)

        # 更新列表显示
        self.update_workpiece_list()

        # 清空输入框和重置状态
        self.clear_inputs()
        self.reset_annotation_state()

        messagebox.showinfo("成功", f"工件样本已添加！\n\n"
                                   f"名称: {workpiece['name']}\n"
                                   f"颜色: {workpiece['color']}\n"
                                   f"形状: {workpiece['shape']}\n\n"
                                   f"可以继续添加更多工件或切换到下一张图片")
    
    def update_workpiece_list(self):
        """更新工件列表显示"""
        self.workpiece_listbox.delete(0, tk.END)
        
        if str(self.current_image_path) in self.annotations:
            for i, workpiece in enumerate(self.annotations[str(self.current_image_path)]):
                display_text = f"{i+1}. {workpiece['name']} ({workpiece['color']} {workpiece['shape']})"
                self.workpiece_listbox.insert(tk.END, display_text)
    
    def clear_inputs(self):
        """清空输入框"""
        self.name_var.set("")
        self.number_var.set("")
        self.color_var.set("")
        self.shape_var.set("")
        self.material_var.set("")
        self.description_text.delete(1.0, tk.END)

    def reset_annotation_state(self):
        """重置标注状态"""
        self.rectangles.clear()
        self.add_button.config(text="1. 先画框选择工件", state="normal")
        self.add_sample_button.config(state="disabled")
        self.status_label.config(text="请先画框选择工件区域", foreground="blue")
    
    def delete_workpiece(self):
        """删除选中的工件"""
        selection = self.workpiece_listbox.curselection()
        if selection:
            index = selection[0]
            if str(self.current_image_path) in self.annotations:
                del self.annotations[str(self.current_image_path)][index]
                self.update_workpiece_list()
    
    def draw_existing_rectangles(self):
        """绘制已有的标注框"""
        if str(self.current_image_path) in self.annotations:
            for i, workpiece in enumerate(self.annotations[str(self.current_image_path)]):
                bbox = workpiece['bbox']
                # 绘制已保存的工件框
                self.canvas.create_rectangle(
                    bbox['x1'], bbox['y1'], bbox['x2'], bbox['y2'],
                    outline="blue", width=2
                )
                # 添加标签
                self.canvas.create_text(
                    bbox['x1'], bbox['y1'] - 10,
                    text=workpiece['name'], fill="blue", anchor=tk.W
                )

    def clear_rectangles(self):
        """清除所有框"""
        self.rectangles.clear()
        # 重新显示图片以清除canvas上的框
        self.display_image()
        # 重置按钮状态
        self.reset_annotation_state()
        messagebox.showinfo("提示", "所有框选区域已清除，可以重新开始标注")
    
    def save_annotation(self):
        """保存标注"""
        if str(self.current_image_path) not in self.annotations:
            messagebox.showwarning("警告", "当前图片没有标注信息")
            return
        
        # 保存为JSON格式
        annotation_file = self.annotation_dir / f"{self.current_image_path.stem}.json"
        
        with open(annotation_file, 'w', encoding='utf-8') as f:
            json.dump(self.annotations[str(self.current_image_path)], f, 
                     ensure_ascii=False, indent=2)
        
        messagebox.showinfo("成功", f"标注已保存到: {annotation_file}")
    
    def load_existing_annotation(self):
        """加载已有标注"""
        annotation_file = self.annotation_dir / f"{self.current_image_path.stem}.json"
        
        if annotation_file.exists():
            try:
                with open(annotation_file, 'r', encoding='utf-8') as f:
                    self.annotations[str(self.current_image_path)] = json.load(f)
                self.update_workpiece_list()
            except Exception as e:
                print(f"加载标注文件失败: {e}")
    
    def prev_image(self):
        """上一张图片"""
        if self.current_index > 0:
            self.current_index -= 1
            self.load_current_image()
            self.reset_annotation_state()

    def next_image(self):
        """下一张图片"""
        if self.current_index < len(self.image_list) - 1:
            self.current_index += 1
            self.load_current_image()
            self.reset_annotation_state()
    
    def run(self):
        """运行应用"""
        self.root.mainloop()

def main():
    """主函数"""
    app = WorkpieceAnnotator()
    app.run()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
自定义工件标注工具
支持详细的工件信息标注：颜色、材料、名称、编号等
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import json
import os
from pathlib import Path
from PIL import Image, ImageTk
import numpy as np

class WorkpieceAnnotator:
    """工件标注器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("工件标注工具")
        self.root.geometry("1200x800")
        
        # 数据
        self.current_image = None
        self.current_image_path = None
        self.image_list = []
        self.current_index = 0
        self.annotations = {}
        self.drawing = False
        self.start_x = 0
        self.start_y = 0
        self.rectangles = []
        
        # 项目路径
        self.project_root = Path(__file__).parent.parent.parent
        self.image_dir = self.project_root / "data" / "raw"
        self.annotation_dir = self.project_root / "data" / "annotations"
        self.annotation_dir.mkdir(exist_ok=True)
        
        self.setup_ui()
        self.load_images()
        
    def setup_ui(self):
        """设置用户界面"""
        
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧图片显示区域
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 图片显示标签
        self.image_label = tk.Label(left_frame, bg="gray")
        self.image_label.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.image_label.bind("<Button-1>", self.start_draw)
        self.image_label.bind("<B1-Motion>", self.draw_rectangle)
        self.image_label.bind("<ButtonRelease-1>", self.end_draw)
        
        # 底部控制按钮
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(control_frame, text="上一张", command=self.prev_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="下一张", command=self.next_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="保存标注", command=self.save_annotation).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清除所有框", command=self.clear_rectangles).pack(side=tk.LEFT, padx=5)
        
        # 右侧标注信息区域
        right_frame = ttk.Frame(main_frame, width=300)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_frame.pack_propagate(False)
        
        # 当前图片信息
        info_frame = ttk.LabelFrame(right_frame, text="当前图片信息")
        info_frame.pack(fill=tk.X, pady=5)
        
        self.image_info_label = ttk.Label(info_frame, text="未加载图片")
        self.image_info_label.pack(pady=5)
        
        # 工件标注信息
        annotation_frame = ttk.LabelFrame(right_frame, text="工件标注信息")
        annotation_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 工件基本信息
        ttk.Label(annotation_frame, text="工件名称:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.name_var = tk.StringVar()
        ttk.Entry(annotation_frame, textvariable=self.name_var, width=20).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(annotation_frame, text="工件编号:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.number_var = tk.StringVar()
        ttk.Entry(annotation_frame, textvariable=self.number_var, width=20).grid(row=1, column=1, padx=5, pady=2)
        
        ttk.Label(annotation_frame, text="颜色:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.color_var = tk.StringVar()
        color_combo = ttk.Combobox(annotation_frame, textvariable=self.color_var, width=18)
        color_combo['values'] = ('红色', '蓝色', '绿色', '黄色', '黑色', '白色', '灰色', '其他')
        color_combo.grid(row=2, column=1, padx=5, pady=2)
        
        ttk.Label(annotation_frame, text="形状:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.shape_var = tk.StringVar()
        shape_combo = ttk.Combobox(annotation_frame, textvariable=self.shape_var, width=18)
        shape_combo['values'] = ('圆形', '方形', '三角形', '长方形', '椭圆形', '不规则', '其他')
        shape_combo.grid(row=3, column=1, padx=5, pady=2)
        
        ttk.Label(annotation_frame, text="材料:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        self.material_var = tk.StringVar()
        material_combo = ttk.Combobox(annotation_frame, textvariable=self.material_var, width=18)
        material_combo['values'] = ('金属', '塑料', '木材', '陶瓷', '玻璃', '橡胶', '其他')
        material_combo.grid(row=4, column=1, padx=5, pady=2)
        
        ttk.Label(annotation_frame, text="描述信息:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=2)
        self.description_text = tk.Text(annotation_frame, width=25, height=4)
        self.description_text.grid(row=6, column=0, columnspan=2, padx=5, pady=2)
        
        # 添加工件按钮
        ttk.Button(annotation_frame, text="添加当前工件", command=self.add_workpiece).grid(row=7, column=0, columnspan=2, pady=10)
        
        # 已标注工件列表
        list_frame = ttk.LabelFrame(right_frame, text="已标注工件")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.workpiece_listbox = tk.Listbox(list_frame)
        self.workpiece_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 删除选中工件按钮
        ttk.Button(list_frame, text="删除选中工件", command=self.delete_workpiece).pack(pady=5)
        
    def load_images(self):
        """加载图片列表"""
        if self.image_dir.exists():
            self.image_list = list(self.image_dir.glob("*.jpg")) + list(self.image_dir.glob("*.png"))
            if self.image_list:
                self.load_current_image()
            else:
                messagebox.showwarning("警告", "图片目录中没有找到图片文件")
        else:
            messagebox.showerror("错误", f"图片目录不存在: {self.image_dir}")
    
    def load_current_image(self):
        """加载当前图片"""
        if not self.image_list:
            return
            
        self.current_image_path = self.image_list[self.current_index]
        
        # 加载图片
        image = cv2.imread(str(self.current_image_path))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 调整图片大小以适应显示
        height, width = image.shape[:2]
        max_width, max_height = 800, 600
        
        if width > max_width or height > max_height:
            scale = min(max_width/width, max_height/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height))
        
        self.current_image = image
        self.display_image()
        
        # 更新图片信息
        info_text = f"图片: {self.current_image_path.name}\n"
        info_text += f"进度: {self.current_index + 1}/{len(self.image_list)}"
        self.image_info_label.config(text=info_text)
        
        # 加载已有标注
        self.load_existing_annotation()
    
    def display_image(self):
        """显示图片"""
        if self.current_image is not None:
            # 转换为PIL图片
            pil_image = Image.fromarray(self.current_image)
            photo = ImageTk.PhotoImage(pil_image)
            
            # 显示图片
            self.image_label.config(image=photo)
            self.image_label.image = photo  # 保持引用
    
    def start_draw(self, event):
        """开始画框"""
        self.drawing = True
        self.start_x = event.x
        self.start_y = event.y
    
    def draw_rectangle(self, event):
        """画框过程"""
        if self.drawing:
            # 这里可以添加实时预览框的功能
            pass
    
    def end_draw(self, event):
        """结束画框"""
        if self.drawing:
            self.drawing = False
            end_x = event.x
            end_y = event.y
            
            # 确保坐标有效
            if abs(end_x - self.start_x) > 10 and abs(end_y - self.start_y) > 10:
                # 添加矩形框
                rect = {
                    'x1': min(self.start_x, end_x),
                    'y1': min(self.start_y, end_y),
                    'x2': max(self.start_x, end_x),
                    'y2': max(self.start_y, end_y)
                }
                self.rectangles.append(rect)
                messagebox.showinfo("提示", "框选完成！请在右侧填写工件信息，然后点击'添加当前工件'")
    
    def add_workpiece(self):
        """添加工件标注"""
        if not self.rectangles:
            messagebox.showwarning("警告", "请先框选工件区域")
            return
        
        if not self.name_var.get().strip():
            messagebox.showwarning("警告", "请输入工件名称")
            return
        
        # 使用最后一个框
        rect = self.rectangles[-1]
        
        workpiece = {
            'name': self.name_var.get().strip(),
            'number': self.number_var.get().strip(),
            'color': self.color_var.get(),
            'shape': self.shape_var.get(),
            'material': self.material_var.get(),
            'description': self.description_text.get(1.0, tk.END).strip(),
            'bbox': rect
        }
        
        # 添加到当前图片的标注中
        if str(self.current_image_path) not in self.annotations:
            self.annotations[str(self.current_image_path)] = []
        
        self.annotations[str(self.current_image_path)].append(workpiece)
        
        # 更新列表显示
        self.update_workpiece_list()
        
        # 清空输入框
        self.clear_inputs()
        
        messagebox.showinfo("成功", "工件标注已添加")
    
    def update_workpiece_list(self):
        """更新工件列表显示"""
        self.workpiece_listbox.delete(0, tk.END)
        
        if str(self.current_image_path) in self.annotations:
            for i, workpiece in enumerate(self.annotations[str(self.current_image_path)]):
                display_text = f"{i+1}. {workpiece['name']} ({workpiece['color']} {workpiece['shape']})"
                self.workpiece_listbox.insert(tk.END, display_text)
    
    def clear_inputs(self):
        """清空输入框"""
        self.name_var.set("")
        self.number_var.set("")
        self.color_var.set("")
        self.shape_var.set("")
        self.material_var.set("")
        self.description_text.delete(1.0, tk.END)
    
    def delete_workpiece(self):
        """删除选中的工件"""
        selection = self.workpiece_listbox.curselection()
        if selection:
            index = selection[0]
            if str(self.current_image_path) in self.annotations:
                del self.annotations[str(self.current_image_path)][index]
                self.update_workpiece_list()
    
    def clear_rectangles(self):
        """清除所有框"""
        self.rectangles.clear()
        messagebox.showinfo("提示", "所有框选区域已清除")
    
    def save_annotation(self):
        """保存标注"""
        if str(self.current_image_path) not in self.annotations:
            messagebox.showwarning("警告", "当前图片没有标注信息")
            return
        
        # 保存为JSON格式
        annotation_file = self.annotation_dir / f"{self.current_image_path.stem}.json"
        
        with open(annotation_file, 'w', encoding='utf-8') as f:
            json.dump(self.annotations[str(self.current_image_path)], f, 
                     ensure_ascii=False, indent=2)
        
        messagebox.showinfo("成功", f"标注已保存到: {annotation_file}")
    
    def load_existing_annotation(self):
        """加载已有标注"""
        annotation_file = self.annotation_dir / f"{self.current_image_path.stem}.json"
        
        if annotation_file.exists():
            try:
                with open(annotation_file, 'r', encoding='utf-8') as f:
                    self.annotations[str(self.current_image_path)] = json.load(f)
                self.update_workpiece_list()
            except Exception as e:
                print(f"加载标注文件失败: {e}")
    
    def prev_image(self):
        """上一张图片"""
        if self.current_index > 0:
            self.current_index -= 1
            self.load_current_image()
    
    def next_image(self):
        """下一张图片"""
        if self.current_index < len(self.image_list) - 1:
            self.current_index += 1
            self.load_current_image()
    
    def run(self):
        """运行应用"""
        self.root.mainloop()

def main():
    """主函数"""
    app = WorkpieceAnnotator()
    app.run()

if __name__ == "__main__":
    main()

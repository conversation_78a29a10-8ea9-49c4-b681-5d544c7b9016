#!/usr/bin/env python3
"""
直接启动LabelImg的简单脚本
"""

import sys
import os
from pathlib import Path

def main():
    """启动LabelImg"""
    print("🏷️ 启动LabelImg标注工具...")
    
    # 项目根目录
    project_root = Path(__file__).parent.parent
    
    # 设置环境变量
    os.environ['LABELIMG_DATA_DIR'] = str(project_root / "data" / "raw")
    os.environ['LABELIMG_SAVE_DIR'] = str(project_root / "data" / "annotations")
    
    print(f"📁 图片目录: {project_root / 'data' / 'raw'}")
    print(f"📝 标注输出: {project_root / 'data' / 'annotations'}")
    print("\n" + "="*50)
    print("📋 LabelImg使用说明:")
    print("1. 点击 'Open Dir' 选择图片目录")
    print("2. 点击 'Change Save Dir' 选择标注保存目录") 
    print("3. 点击左下角 'PascalVOC' 切换到 'YOLO' 格式")
    print("4. 按 'W' 键开始画框标注")
    print("5. 选择正确的工件类别")
    print("6. 按 'Ctrl+S' 保存当前标注")
    print("7. 按 'D' 键切换到下一张图片")
    print("="*50)
    
    try:
        # 直接导入并启动LabelImg
        from labelImg import labelImg
        
        # 启动应用
        app = labelImg.QApplication(sys.argv)
        win = labelImg.MainWindow()
        win.show()
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n💡 手动启动方法:")
        print("python -c \"from labelImg import labelImg; import sys; app = labelImg.QApplication(sys.argv); win = labelImg.MainWindow(); win.show(); sys.exit(app.exec_())\"")

if __name__ == "__main__":
    main()

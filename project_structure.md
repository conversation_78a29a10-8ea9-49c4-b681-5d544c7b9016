# YOLO11工件识别项目架构

## 项目目录结构
```
YOLO11/
├── data/                          # 数据集目录
│   ├── raw/                       # 原始图片 (从tupian移动过来)
│   ├── annotations/               # 标注文件 (YOLO格式)
│   ├── train/                     # 训练集
│   │   ├── images/
│   │   └── labels/
│   ├── val/                       # 验证集
│   │   ├── images/
│   │   └── labels/
│   └── test/                      # 测试集
│       ├── images/
│       └── labels/
├── models/                        # 模型相关
│   ├── pretrained/                # 预训练模型
│   ├── trained/                   # 训练好的模型
│   └── configs/                   # 模型配置文件
├── src/                           # 源代码
│   ├── data_processing/           # 数据处理
│   │   ├── __init__.py
│   │   ├── data_splitter.py       # 数据集划分
│   │   └── data_augmentation.py   # 数据增强
│   ├── training/                  # 训练相关
│   │   ├── __init__.py
│   │   ├── train.py               # 训练脚本
│   │   └── validate.py            # 验证脚本
│   ├── inference/                 # 推理相关
│   │   ├── __init__.py
│   │   ├── detector.py            # 检测器类
│   │   └── batch_inference.py     # 批量推理
│   ├── annotation_manager/        # 标注信息管理
│   │   ├── __init__.py
│   │   ├── annotation_config.py   # 标注配置
│   │   └── annotation_display.py  # 标注显示
│   └── utils/                     # 工具函数
│       ├── __init__.py
│       ├── visualization.py       # 可视化工具
│       └── file_utils.py          # 文件操作工具
├── configs/                       # 配置文件
│   ├── dataset.yaml               # 数据集配置
│   ├── model_config.yaml          # 模型配置
│   └── annotation_config.json     # 标注信息配置
├── scripts/                       # 脚本文件
│   ├── setup_data.py              # 数据准备脚本
│   ├── train_model.py             # 训练启动脚本
│   └── run_inference.py           # 推理启动脚本
├── results/                       # 结果输出
│   ├── training_logs/             # 训练日志
│   ├── predictions/               # 预测结果
│   └── visualizations/            # 可视化结果
├── docs/                          # 文档 (已存在)
├── requirements.txt               # 依赖列表
└── README.md                      # 项目说明
```

## 核心模块设计

### 1. 数据处理模块 (data_processing)
- **data_splitter.py**: 将标注好的数据按8:1:1划分训练/验证/测试集
- **data_augmentation.py**: 数据增强策略(旋转、缩放、亮度调整等)

### 2. 训练模块 (training)  
- **train.py**: YOLO11训练主脚本
- **validate.py**: 模型验证和性能评估

### 3. 推理模块 (inference)
- **detector.py**: 工件检测器核心类
- **batch_inference.py**: 批量图片处理

### 4. 标注管理模块 (annotation_manager)
- **annotation_config.py**: 管理工件类别和自定义标注信息
- **annotation_display.py**: 检测结果显示逻辑

### 5. 工具模块 (utils)
- **visualization.py**: 结果可视化
- **file_utils.py**: 文件操作辅助函数

## 配置文件设计

### dataset.yaml (YOLO格式)
```yaml
path: ./data
train: train/images
val: val/images
test: test/images

nc: 3  # 类别数量
names: ['工件A', '工件B', '工件C']
```

### annotation_config.json
```json
{
  "工件A": {
    "display_name": "红色圆形工件",
    "description": "用户自定义描述信息",
    "color": "#FF0000"
  },
  "工件B": {
    "display_name": "蓝色方形工件", 
    "description": "用户自定义描述信息",
    "color": "#0000FF"
  },
  "工件C": {
    "display_name": "绿色三角形工件",
    "description": "用户自定义描述信息", 
    "color": "#00FF00"
  }
}
```

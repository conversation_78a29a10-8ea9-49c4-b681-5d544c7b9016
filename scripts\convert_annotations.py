#!/usr/bin/env python3
"""
标注转换启动脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("🔄 标注格式转换工具")
    print("="*50)
    print("📋 功能说明:")
    print("✅ 将自定义JSON标注转换为YOLO格式")
    print("✅ 自动分析工件类别")
    print("✅ 生成YOLO训练所需的数据集")
    print("✅ 更新数据集配置文件")
    print("="*50)
    
    try:
        from src.data_processing.annotation_converter import AnnotationConverter
        
        # 创建转换器
        converter = AnnotationConverter(str(project_root))
        
        # 执行转换
        converter.create_yolo_dataset()
        
        print("\n🎉 转换完成!")
        print("📋 下一步操作:")
        print("1. 运行数据集划分: python src/data_processing/data_splitter.py")
        print("2. 开始模型训练: python scripts/train_model.py")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

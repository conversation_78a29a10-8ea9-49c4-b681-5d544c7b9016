#!/usr/bin/env python3
"""
标注格式转换器
将自定义JSON标注格式转换为YOLO训练格式
"""

import json
import os
from pathlib import Path
from PIL import Image
import shutil

class AnnotationConverter:
    """标注格式转换器"""
    
    def __init__(self, project_root: str):
        """
        初始化转换器
        
        Args:
            project_root: 项目根目录
        """
        self.project_root = Path(project_root)
        self.data_root = self.project_root / "data"
        self.raw_dir = self.data_root / "raw"
        self.annotations_dir = self.data_root / "annotations"
        self.yolo_annotations_dir = self.data_root / "yolo_annotations"
        
        # 创建YOLO标注目录
        self.yolo_annotations_dir.mkdir(exist_ok=True)
        
        # 类别映射 (根据工件特征自动分类)
        self.class_mapping = {}
        self.class_names = []
        
    def analyze_workpieces(self):
        """分析所有工件，建立类别映射"""
        print("🔍 分析工件类别...")
        
        all_workpieces = []
        
        # 收集所有标注文件中的工件信息
        for json_file in self.annotations_dir.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    workpieces = json.load(f)
                    all_workpieces.extend(workpieces)
            except Exception as e:
                print(f"⚠️ 读取标注文件失败 {json_file}: {e}")
        
        if not all_workpieces:
            print("❌ 没有找到标注数据")
            return
        
        # 根据颜色和形状组合创建类别
        unique_combinations = set()
        for wp in all_workpieces:
            color = wp.get('color', '未知')
            shape = wp.get('shape', '未知')
            combination = f"{color}_{shape}"
            unique_combinations.add(combination)
        
        # 建立类别映射
        self.class_names = sorted(list(unique_combinations))
        self.class_mapping = {name: idx for idx, name in enumerate(self.class_names)}
        
        print(f"📊 发现 {len(self.class_names)} 个工件类别:")
        for idx, name in enumerate(self.class_names):
            print(f"  {idx}: {name}")
        
        # 保存类别文件
        classes_file = self.data_root / "classes.txt"
        with open(classes_file, 'w', encoding='utf-8') as f:
            for name in self.class_names:
                f.write(f"{name}\n")
        
        print(f"✅ 类别文件已保存: {classes_file}")
    
    def convert_single_annotation(self, json_file: Path, image_file: Path):
        """
        转换单个标注文件
        
        Args:
            json_file: JSON标注文件路径
            image_file: 对应的图片文件路径
        """
        try:
            # 读取JSON标注
            with open(json_file, 'r', encoding='utf-8') as f:
                workpieces = json.load(f)
            
            if not workpieces:
                return
            
            # 获取图片尺寸
            with Image.open(image_file) as img:
                img_width, img_height = img.size
            
            # 转换为YOLO格式
            yolo_annotations = []
            
            for wp in workpieces:
                # 获取类别
                color = wp.get('color', '未知')
                shape = wp.get('shape', '未知')
                class_name = f"{color}_{shape}"
                
                if class_name not in self.class_mapping:
                    print(f"⚠️ 未知类别: {class_name}")
                    continue
                
                class_id = self.class_mapping[class_name]
                
                # 获取边界框
                bbox = wp['bbox']
                x1, y1, x2, y2 = bbox['x1'], bbox['y1'], bbox['x2'], bbox['y2']
                
                # 转换为YOLO格式 (归一化的中心点坐标和宽高)
                center_x = (x1 + x2) / 2 / img_width
                center_y = (y1 + y2) / 2 / img_height
                width = (x2 - x1) / img_width
                height = (y2 - y1) / img_height
                
                # 确保坐标在有效范围内
                center_x = max(0, min(1, center_x))
                center_y = max(0, min(1, center_y))
                width = max(0, min(1, width))
                height = max(0, min(1, height))
                
                yolo_annotations.append(f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}")
            
            # 保存YOLO格式标注文件
            yolo_file = self.yolo_annotations_dir / f"{image_file.stem}.txt"
            with open(yolo_file, 'w') as f:
                f.write('\n'.join(yolo_annotations))
            
            print(f"✅ 转换完成: {json_file.name} -> {yolo_file.name}")
            
        except Exception as e:
            print(f"❌ 转换失败 {json_file}: {e}")
    
    def convert_all_annotations(self):
        """转换所有标注文件"""
        print("🔄 开始转换标注格式...")
        
        # 首先分析工件类别
        self.analyze_workpieces()
        
        if not self.class_mapping:
            print("❌ 没有有效的类别映射，无法转换")
            return
        
        # 转换每个标注文件
        converted_count = 0
        
        for json_file in self.annotations_dir.glob("*.json"):
            # 找到对应的图片文件
            image_file = None
            for ext in ['.jpg', '.png', '.jpeg']:
                potential_image = self.raw_dir / f"{json_file.stem}{ext}"
                if potential_image.exists():
                    image_file = potential_image
                    break
            
            if image_file:
                self.convert_single_annotation(json_file, image_file)
                converted_count += 1
            else:
                print(f"⚠️ 找不到对应的图片文件: {json_file.stem}")
        
        print(f"🎉 转换完成! 共转换 {converted_count} 个标注文件")
        
        # 更新数据集配置文件
        self.update_dataset_config()
    
    def update_dataset_config(self):
        """更新数据集配置文件"""
        config_file = self.project_root / "configs" / "dataset.yaml"
        
        config_content = f"""# YOLO11工件识别数据集配置文件

# 数据集路径配置
path: ./data  # 数据集根目录
train: train/images  # 训练集图片路径
val: val/images      # 验证集图片路径  
test: test/images    # 测试集图片路径

# 类别配置
nc: {len(self.class_names)}  # 类别数量
names: 
"""
        
        # 添加类别名称
        for idx, name in enumerate(self.class_names):
            config_content += f"  {idx}: '{name}'\n"
        
        config_content += """
# 数据增强配置 (可选)
augment: true
mosaic: 0.5
mixup: 0.1
copy_paste: 0.1
degrees: 10.0
translate: 0.1
scale: 0.5
shear: 2.0
perspective: 0.0
flipud: 0.0
fliplr: 0.5
hsv_h: 0.015
hsv_s: 0.7
hsv_v: 0.4"""
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"✅ 数据集配置已更新: {config_file}")
    
    def create_yolo_dataset(self):
        """创建完整的YOLO数据集结构"""
        print("📁 创建YOLO数据集结构...")
        
        # 首先转换标注
        self.convert_all_annotations()
        
        # 复制图片和标注到统一目录
        yolo_images_dir = self.data_root / "yolo_dataset" / "images"
        yolo_labels_dir = self.data_root / "yolo_dataset" / "labels"
        
        yolo_images_dir.mkdir(parents=True, exist_ok=True)
        yolo_labels_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制文件
        copied_count = 0
        for txt_file in self.yolo_annotations_dir.glob("*.txt"):
            # 复制标注文件
            shutil.copy2(txt_file, yolo_labels_dir / txt_file.name)
            
            # 复制对应的图片文件
            for ext in ['.jpg', '.png', '.jpeg']:
                img_file = self.raw_dir / f"{txt_file.stem}{ext}"
                if img_file.exists():
                    shutil.copy2(img_file, yolo_images_dir / img_file.name)
                    copied_count += 1
                    break
        
        print(f"✅ YOLO数据集创建完成! 共 {copied_count} 个样本")
        print(f"📁 图片目录: {yolo_images_dir}")
        print(f"📁 标注目录: {yolo_labels_dir}")

def main():
    """主函数"""
    # 项目根目录
    project_root = Path(__file__).parent.parent.parent
    
    # 创建转换器
    converter = AnnotationConverter(str(project_root))
    
    # 执行转换
    converter.create_yolo_dataset()

if __name__ == "__main__":
    main()

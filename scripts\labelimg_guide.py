#!/usr/bin/env python3
"""
LabelImg详细使用指导脚本
"""

import os
import sys
from pathlib import Path

def print_guide():
    """打印详细的LabelImg使用指导"""
    
    project_root = Path(__file__).parent.parent
    
    print("🏷️ LabelImg详细使用指导")
    print("="*60)
    
    print("\n📁 目录设置:")
    print(f"图片目录: {project_root / 'data' / 'raw'}")
    print(f"标注保存: {project_root / 'data' / 'annotations'}")
    
    print("\n🔧 LabelImg设置步骤:")
    print("1. 启动LabelImg后，界面应该显示菜单栏和工具栏")
    print("2. 点击菜单 'File' → 'Open Dir' (或按Ctrl+U)")
    print("3. 选择图片文件夹: data/raw")
    print("4. 点击菜单 'File' → 'Change Save Dir' (或按Ctrl+R)")  
    print("5. 选择标注保存文件夹: data/annotations")
    print("6. **重要**: 点击左下角的 'PascalVOC' 按钮，切换到 'YOLO' 格式")
    
    print("\n🖱️ 标注操作:")
    print("1. 按 'W' 键 (或点击工具栏的矩形图标) 进入画框模式")
    print("2. 用鼠标左键拖拽画出矩形框，框选工件")
    print("3. 松开鼠标后会弹出类别选择对话框")
    print("4. 选择对应的工件类别 (工件A/工件B/工件C)")
    print("5. 按 'Ctrl+S' 保存当前标注")
    print("6. 按 'D' 键切换到下一张图片")
    
    print("\n⚠️ 常见问题解决:")
    print("- 如果看不到工具栏: 检查菜单 'View' → 确保工具栏已勾选")
    print("- 如果无法画框: 确保已按 'W' 键或点击矩形工具")
    print("- 如果没有类别选项: 确保classes.txt文件存在且格式正确")
    print("- 如果保存失败: 确保标注保存目录有写入权限")
    
    print("\n🎯 我们的工件类别:")
    print("- 工件A: 红色圆形")
    print("- 工件B: 蓝色方形")  
    print("- 工件C: 绿色三角形")
    
    print("\n" + "="*60)

def launch_labelimg_with_settings():
    """启动LabelImg并尝试预设置"""
    
    project_root = Path(__file__).parent.parent
    
    # 确保目录存在
    (project_root / "data" / "annotations").mkdir(exist_ok=True)
    
    print_guide()
    
    print("\n🚀 正在启动LabelImg...")
    
    try:
        from labelImg import labelImg
        import sys
        
        # 设置命令行参数来预配置LabelImg
        sys.argv = [
            'labelImg',
            str(project_root / "data" / "raw"),  # 图片目录
            str(project_root / "data" / "classes.txt"),  # 类别文件
            str(project_root / "data" / "annotations")  # 保存目录
        ]
        
        app = labelImg.QApplication(sys.argv)
        win = labelImg.MainWindow(
            defaultFilename=str(project_root / "data" / "classes.txt"),
            defaultPrefdefClassFile=str(project_root / "data" / "classes.txt"),
            defaultSaveDir=str(project_root / "data" / "annotations")
        )
        win.show()
        
        print("✅ LabelImg已启动！请按照上述指导进行标注。")
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n💡 请手动启动LabelImg并按照上述指导操作")

if __name__ == "__main__":
    launch_labelimg_with_settings()
